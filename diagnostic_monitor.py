#!/usr/bin/env python3
"""
Diagnostic Monitor for Email Processing Program
Monitors system resources, process health, and captures diagnostic information
to help identify silent program termination issues.
"""

import os
import sys
import time
import json
import signal
import psutil
import threading
import subprocess
from datetime import datetime
from pathlib import Path

class ProcessMonitor:
    """Monitor a process and capture diagnostic information"""
    
    def __init__(self, process_name="python", script_name="email_processor.py"):
        self.process_name = process_name
        self.script_name = script_name
        self.target_process = None
        self.monitoring = False
        self.log_file = f"diagnostic_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        self.data_points = []
        self.start_time = None
        
    def find_target_process(self):
        """Find the target email processing process"""
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if (proc.info['name'] and self.process_name.lower() in proc.info['name'].lower() and
                    proc.info['cmdline'] and any(self.script_name in arg for arg in proc.info['cmdline'])):
                    return proc
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return None
    
    def get_system_info(self):
        """Get current system resource information"""
        try:
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=0.1)
            disk = psutil.disk_usage('/')
            
            return {
                'timestamp': datetime.now().isoformat(),
                'memory_total_gb': memory.total / (1024**3),
                'memory_available_gb': memory.available / (1024**3),
                'memory_used_percent': memory.percent,
                'cpu_percent': cpu_percent,
                'disk_free_gb': disk.free / (1024**3),
                'disk_used_percent': (disk.used / disk.total) * 100
            }
        except Exception as e:
            return {'error': f'System info failed: {e}', 'timestamp': datetime.now().isoformat()}
    
    def get_process_info(self, process):
        """Get detailed process information"""
        try:
            memory_info = process.memory_info()
            cpu_percent = process.cpu_percent()
            
            # Get thread count
            try:
                thread_count = process.num_threads()
            except:
                thread_count = -1
            
            # Get file descriptors (Unix only)
            try:
                fd_count = process.num_fds() if hasattr(process, 'num_fds') else -1
            except:
                fd_count = -1
            
            # Get process status
            try:
                status = process.status()
            except:
                status = "unknown"
            
            return {
                'pid': process.pid,
                'status': status,
                'memory_rss_mb': memory_info.rss / (1024 * 1024),
                'memory_vms_mb': memory_info.vms / (1024 * 1024),
                'memory_percent': process.memory_percent(),
                'cpu_percent': cpu_percent,
                'thread_count': thread_count,
                'fd_count': fd_count,
                'create_time': process.create_time(),
                'runtime_seconds': time.time() - process.create_time()
            }
        except Exception as e:
            return {'error': f'Process info failed: {e}', 'pid': getattr(process, 'pid', -1)}
    
    def check_critical_conditions(self, system_info, process_info):
        """Check for critical conditions that might cause termination"""
        warnings = []
        critical = []
        
        # Memory warnings
        if system_info.get('memory_available_gb', 0) < 0.5:
            critical.append(f"Critical: System memory < 0.5GB ({system_info.get('memory_available_gb', 0):.2f}GB)")
        elif system_info.get('memory_available_gb', 0) < 1.0:
            warnings.append(f"Warning: Low system memory ({system_info.get('memory_available_gb', 0):.2f}GB)")
        
        if process_info.get('memory_rss_mb', 0) > 4096:
            critical.append(f"Critical: Process memory > 4GB ({process_info.get('memory_rss_mb', 0):.1f}MB)")
        elif process_info.get('memory_rss_mb', 0) > 2048:
            warnings.append(f"Warning: High process memory ({process_info.get('memory_rss_mb', 0):.1f}MB)")
        
        # CPU warnings
        if system_info.get('cpu_percent', 0) > 95:
            warnings.append(f"Warning: High CPU usage ({system_info.get('cpu_percent', 0):.1f}%)")
        
        # Thread warnings
        if process_info.get('thread_count', 0) > 20:
            warnings.append(f"Warning: High thread count ({process_info.get('thread_count', 0)})")
        
        # File descriptor warnings (Unix only)
        if process_info.get('fd_count', 0) > 1000:
            warnings.append(f"Warning: High file descriptor count ({process_info.get('fd_count', 0)})")
        
        return warnings, critical
    
    def log_data_point(self, system_info, process_info, warnings, critical):
        """Log a data point to the monitoring log"""
        data_point = {
            'timestamp': datetime.now().isoformat(),
            'runtime_seconds': time.time() - self.start_time if self.start_time else 0,
            'system': system_info,
            'process': process_info,
            'warnings': warnings,
            'critical': critical
        }
        
        self.data_points.append(data_point)
        
        # Write to file every 10 data points or if critical
        if len(self.data_points) % 10 == 0 or critical:
            self.save_log()
    
    def save_log(self):
        """Save monitoring data to JSON file"""
        try:
            with open(self.log_file, 'w') as f:
                json.dump({
                    'monitor_start': self.start_time,
                    'total_data_points': len(self.data_points),
                    'data_points': self.data_points
                }, f, indent=2)
        except Exception as e:
            print(f"Failed to save log: {e}")
    
    def monitor_process(self, interval=5):
        """Main monitoring loop"""
        print(f"Starting diagnostic monitor...")
        print(f"Looking for process: {self.process_name} running {self.script_name}")
        print(f"Log file: {self.log_file}")
        print(f"Monitoring interval: {interval} seconds")
        print("-" * 60)
        
        self.monitoring = True
        self.start_time = time.time()
        
        while self.monitoring:
            try:
                # Find target process
                if not self.target_process or not self.target_process.is_running():
                    self.target_process = self.find_target_process()
                    if not self.target_process:
                        print(f"[{datetime.now().strftime('%H:%M:%S')}] Waiting for target process...")
                        time.sleep(interval)
                        continue
                    else:
                        print(f"[{datetime.now().strftime('%H:%M:%S')}] Found target process PID: {self.target_process.pid}")
                
                # Collect system and process information
                system_info = self.get_system_info()
                process_info = self.get_process_info(self.target_process)
                
                # Check for critical conditions
                warnings, critical = self.check_critical_conditions(system_info, process_info)
                
                # Display current status
                timestamp = datetime.now().strftime('%H:%M:%S')
                memory_mb = process_info.get('memory_rss_mb', 0)
                cpu_pct = process_info.get('cpu_percent', 0)
                threads = process_info.get('thread_count', 0)
                sys_mem_gb = system_info.get('memory_available_gb', 0)
                
                status_line = (f"[{timestamp}] PID:{self.target_process.pid} "
                             f"Mem:{memory_mb:.1f}MB CPU:{cpu_pct:.1f}% "
                             f"Threads:{threads} SysMem:{sys_mem_gb:.1f}GB")
                
                if critical:
                    print(f"🚨 CRITICAL: {status_line}")
                    for msg in critical:
                        print(f"   {msg}")
                elif warnings:
                    print(f"⚠️  WARNING: {status_line}")
                    for msg in warnings:
                        print(f"   {msg}")
                else:
                    print(f"✅ OK: {status_line}")
                
                # Log data point
                self.log_data_point(system_info, process_info, warnings, critical)
                
                time.sleep(interval)
                
            except psutil.NoSuchProcess:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] Target process terminated!")
                self.save_log()
                break
            except KeyboardInterrupt:
                print("\nMonitoring interrupted by user")
                break
            except Exception as e:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] Monitor error: {e}")
                time.sleep(interval)
        
        self.save_log()
        print(f"\nMonitoring completed. Log saved to: {self.log_file}")

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Monitor email processing program for diagnostic information")
    parser.add_argument("--interval", "-i", type=int, default=5, help="Monitoring interval in seconds (default: 5)")
    parser.add_argument("--process", "-p", default="python", help="Process name to monitor (default: python)")
    parser.add_argument("--script", "-s", default="email_processor.py", help="Script name to identify (default: email_processor.py)")
    
    args = parser.parse_args()
    
    monitor = ProcessMonitor(args.process, args.script)
    
    # Set up signal handler for graceful shutdown
    def signal_handler(signum, frame):
        print(f"\nReceived signal {signum}, stopping monitor...")
        monitor.monitoring = False
    
    signal.signal(signal.SIGINT, signal_handler)
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        monitor.monitor_process(args.interval)
    except Exception as e:
        print(f"Monitor failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
