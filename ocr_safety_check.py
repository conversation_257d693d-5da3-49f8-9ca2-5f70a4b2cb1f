#!/usr/bin/env python3
"""
OCR Safety Check - Test PaddleOCR initialization before running the main application
This script helps identify OCR-related issues that could cause silent termination
"""

import os
import sys
import time
import subprocess
import tempfile
from pathlib import Path

def test_paddleocr_import():
    """Test if PaddleOCR can be imported without crashing"""
    print("Testing PaddleOCR import...")
    
    test_script = '''
import sys
import warnings
warnings.filterwarnings("ignore")

try:
    from paddleocr import PaddleOCR
    print("IMPORT_SUCCESS")
    sys.exit(0)
except ImportError as e:
    print(f"IMPORT_ERROR: {e}")
    sys.exit(1)
except Exception as e:
    print(f"IMPORT_CRASH: {e}")
    sys.exit(2)
'''
    
    try:
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(test_script)
            script_path = f.name
        
        try:
            result = subprocess.run(
                [sys.executable, script_path],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0 and "IMPORT_SUCCESS" in result.stdout:
                print("✅ PaddleOCR import successful")
                return True
            elif result.returncode == 1:
                print(f"❌ PaddleOCR import failed: {result.stdout}")
                return False
            else:
                print(f"❌ PaddleOCR import crashed: {result.stdout}")
                return False
                
        finally:
            os.unlink(script_path)
            
    except subprocess.TimeoutExpired:
        print("❌ PaddleOCR import test timed out")
        return False
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def test_paddleocr_initialization():
    """Test if PaddleOCR can be initialized without crashing"""
    print("Testing PaddleOCR initialization...")
    
    test_script = '''
import sys
import warnings
import numpy as np
warnings.filterwarnings("ignore")

try:
    # Redirect output to minimize noise
    import io
    old_stdout = sys.stdout
    old_stderr = sys.stderr
    sys.stdout = io.StringIO()
    sys.stderr = io.StringIO()
    
    from paddleocr import PaddleOCR
    
    # Try to initialize with minimal configuration
    ocr = PaddleOCR(lang='ch', use_gpu=False, show_log=False)
    
    # Test with a simple image
    test_image = np.ones((50, 50, 3), dtype=np.uint8) * 255
    result = ocr.ocr(test_image, cls=False)
    
    # Restore output
    sys.stdout = old_stdout
    sys.stderr = old_stderr
    
    print("INIT_SUCCESS")
    sys.exit(0)
    
except Exception as e:
    # Restore output
    try:
        sys.stdout = old_stdout
        sys.stderr = old_stderr
    except:
        pass
    
    print(f"INIT_ERROR: {e}")
    sys.exit(1)
'''
    
    try:
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(test_script)
            script_path = f.name
        
        try:
            print("  Initializing PaddleOCR (this may take a moment)...")
            result = subprocess.run(
                [sys.executable, script_path],
                capture_output=True,
                text=True,
                timeout=120  # 2 minute timeout for initialization
            )
            
            if result.returncode == 0 and "INIT_SUCCESS" in result.stdout:
                print("✅ PaddleOCR initialization successful")
                return True
            else:
                print(f"❌ PaddleOCR initialization failed: {result.stdout}")
                if result.stderr:
                    print(f"   Error details: {result.stderr}")
                return False
                
        finally:
            os.unlink(script_path)
            
    except subprocess.TimeoutExpired:
        print("❌ PaddleOCR initialization test timed out")
        print("   This suggests the OCR service may hang during initialization")
        return False
    except Exception as e:
        print(f"❌ Initialization test failed: {e}")
        return False

def test_system_resources():
    """Check system resources that might affect OCR performance"""
    print("Checking system resources...")
    
    try:
        import psutil
        
        # Check memory
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        available_gb = memory.available / (1024**3)
        
        print(f"  Total memory: {memory_gb:.1f} GB")
        print(f"  Available memory: {available_gb:.1f} GB")
        
        if available_gb < 2.0:
            print("  ⚠️  Warning: Low available memory (< 2GB)")
            print("     OCR processing may fail due to insufficient memory")
        elif available_gb < 4.0:
            print("  ⚠️  Warning: Limited available memory (< 4GB)")
            print("     Consider processing smaller batches")
        else:
            print("  ✅ Sufficient memory available")
        
        # Check CPU
        cpu_count = psutil.cpu_count()
        print(f"  CPU cores: {cpu_count}")
        
        # Check disk space
        disk = psutil.disk_usage('.')
        disk_free_gb = disk.free / (1024**3)
        print(f"  Free disk space: {disk_free_gb:.1f} GB")
        
        if disk_free_gb < 1.0:
            print("  ⚠️  Warning: Low disk space (< 1GB)")
            print("     OCR cache and temporary files may fail")
        
        return True
        
    except ImportError:
        print("  ⚠️  psutil not available - cannot check system resources")
        return True
    except Exception as e:
        print(f"  ❌ Resource check failed: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are available"""
    print("Checking dependencies...")
    
    dependencies = [
        ('paddleocr', 'PaddleOCR'),
        ('PIL', 'Pillow'),
        ('numpy', 'NumPy'),
        ('cv2', 'OpenCV')
    ]
    
    all_available = True
    
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"  ✅ {name} available")
        except ImportError:
            print(f"  ❌ {name} not available")
            all_available = False
    
    return all_available

def main():
    """Run all safety checks"""
    print("OCR Safety Check")
    print("=" * 50)
    
    checks = [
        ("Dependencies", check_dependencies),
        ("System Resources", test_system_resources),
        ("PaddleOCR Import", test_paddleocr_import),
        ("PaddleOCR Initialization", test_paddleocr_initialization)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        print(f"\n{check_name}:")
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"  ❌ Check failed with exception: {e}")
            results.append((check_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("SAFETY CHECK SUMMARY")
    print("=" * 50)
    
    all_passed = True
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {check_name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All safety checks passed!")
        print("The email processor should be safe to run with OCR enabled.")
        return 0
    else:
        print("\n⚠️  Some safety checks failed!")
        print("The email processor may experience issues or silent termination.")
        print("Consider running with OCR disabled or fixing the identified issues.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
