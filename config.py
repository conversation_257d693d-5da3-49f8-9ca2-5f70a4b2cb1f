"""
Configuration Management for Email Batch Processor
Handles OCR cache settings and other application configurations
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class CacheConfig:
    """OCR Cache configuration settings"""
    enabled: bool = True
    cache_dir: str = ".cache"
    expiration_days: int = 30
    max_cache_size_mb: int = 1024
    cleanup_on_startup: bool = True


@dataclass
class AppConfig:
    """Main application configuration"""
    cache: CacheConfig

    # Threading configuration
    max_worker_threads: int = 4
    enable_threading: bool = True

    # OCR configuration
    ocr_enabled: bool = True
    ocr_timeout_seconds: int = 300

    # Export configuration
    excel_optimization: bool = True

    # GUI/logging preferences
    gui_log_colorize: bool = True

    def __post_init__(self):
        """Validate configuration after initialization"""
        if self.cache.expiration_days < 1:
            self.cache.expiration_days = 1
        if self.cache.max_cache_size_mb < 10:
            self.cache.max_cache_size_mb = 10
        if self.max_worker_threads < 1:
            self.max_worker_threads = 1


class ConfigManager:
    """Configuration manager with file persistence and environment variable support"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = Path(config_file)
        self._config: Optional[AppConfig] = None
        self._load_config()
    
    def _get_default_config(self) -> AppConfig:
        """Get default configuration"""
        return AppConfig(
            cache=CacheConfig()
        )
    
    def _load_config(self):
        """Load configuration from file and environment variables"""
        # Start with defaults
        config_dict = asdict(self._get_default_config())
        
        # Load from file if it exists
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    self._merge_config(config_dict, file_config)
            except Exception as e:
                print(f"⚠️ Warning: Failed to load config file {self.config_file}: {e}")
        
        # Override with environment variables
        self._apply_env_overrides(config_dict)
        
        # Create config object
        try:
            cache_config = CacheConfig(**config_dict['cache'])
            self._config = AppConfig(
                cache=cache_config,
                max_worker_threads=config_dict.get('max_worker_threads', 4),
                enable_threading=config_dict.get('enable_threading', True),
                ocr_enabled=config_dict.get('ocr_enabled', True),
                ocr_timeout_seconds=config_dict.get('ocr_timeout_seconds', 300),
                excel_optimization=config_dict.get('excel_optimization', True),
                gui_log_colorize=config_dict.get('gui_log_colorize', True)
            )
        except Exception as e:
            print(f"⚠️ Warning: Invalid configuration, using defaults: {e}")
            self._config = self._get_default_config()
    
    def _merge_config(self, base: Dict[str, Any], override: Dict[str, Any]):
        """Recursively merge configuration dictionaries"""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
    
    def _apply_env_overrides(self, config_dict: Dict[str, Any]):
        """Apply environment variable overrides"""
        env_mappings = {
            'EML_CACHE_ENABLED': ('cache', 'enabled', lambda x: x.lower() == 'true'),
            'EML_CACHE_DIR': ('cache', 'cache_dir', str),
            'EML_CACHE_EXPIRATION_DAYS': ('cache', 'expiration_days', int),
            'EML_CACHE_MAX_SIZE_MB': ('cache', 'max_cache_size_mb', int),
            'EML_CACHE_CLEANUP_ON_STARTUP': ('cache', 'cleanup_on_startup', lambda x: x.lower() == 'true'),
            'EML_MAX_WORKER_THREADS': (None, 'max_worker_threads', int),
            'EML_ENABLE_THREADING': (None, 'enable_threading', lambda x: x.lower() == 'true'),
            'EML_OCR_ENABLED': (None, 'ocr_enabled', lambda x: x.lower() == 'true'),
            'EML_OCR_TIMEOUT_SECONDS': (None, 'ocr_timeout_seconds', int),
            'EML_EXCEL_OPTIMIZATION': (None, 'excel_optimization', lambda x: x.lower() == 'true'),
            'EML_GUI_LOG_COLORIZE': (None, 'gui_log_colorize', lambda x: x.lower() == 'true'),
        }
        
        for env_var, (section, key, converter) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                try:
                    converted_value = converter(value)
                    if section:
                        config_dict[section][key] = converted_value
                    else:
                        config_dict[key] = converted_value
                except (ValueError, TypeError) as e:
                    print(f"⚠️ Warning: Invalid value for {env_var}: {value} ({e})")
    
    def get_config(self) -> AppConfig:
        """Get current configuration"""
        return self._config
    
    def save_config(self):
        """Save current configuration to file"""
        try:
            config_dict = asdict(self._config)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2)
            print(f"✅ Configuration saved to {self.config_file}")
        except Exception as e:
            print(f"❌ Failed to save configuration: {e}")
    
    def update_cache_config(self, **kwargs):
        """Update cache configuration"""
        for key, value in kwargs.items():
            if hasattr(self._config.cache, key):
                setattr(self._config.cache, key, value)
            else:
                print(f"⚠️ Warning: Unknown cache config key: {key}")
    
    def get_cache_config(self) -> CacheConfig:
        """Get cache configuration"""
        return self._config.cache
    
    def print_config(self):
        """Print current configuration"""
        print("\n📋 Current Configuration:")
        print("=" * 50)
        
        print(f"Cache Settings:")
        print(f"  Enabled: {self._config.cache.enabled}")
        print(f"  Directory: {self._config.cache.cache_dir}")
        print(f"  Expiration: {self._config.cache.expiration_days} days")
        print(f"  Max Size: {self._config.cache.max_cache_size_mb} MB")
        print(f"  Cleanup on Startup: {self._config.cache.cleanup_on_startup}")
        
        print(f"\nThreading Settings:")
        print(f"  Enabled: {self._config.enable_threading}")
        print(f"  Max Workers: {self._config.max_worker_threads}")
        
        print(f"\nOCR Settings:")
        print(f"  Enabled: {self._config.ocr_enabled}")
        print(f"  Timeout: {self._config.ocr_timeout_seconds} seconds")
        
        print(f"\nExport Settings:")
        print(f"  Excel Optimization: {self._config.excel_optimization}")
        print("=" * 50)


# Global configuration manager instance
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """Get the global configuration manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def get_config() -> AppConfig:
    """Get current application configuration"""
    return get_config_manager().get_config()


def get_cache_config() -> CacheConfig:
    """Get cache configuration"""
    return get_config_manager().get_cache_config()


def initialize_config(config_file: str = "config.json") -> ConfigManager:
    """Initialize configuration manager with specific config file"""
    global _config_manager
    _config_manager = ConfigManager(config_file)
    return _config_manager


def print_config():
    """Print current configuration"""
    get_config_manager().print_config()


# Environment variable documentation
ENV_VARS_HELP = """
Environment Variables for Email Batch Processor:

Cache Configuration:
  EML_CACHE_ENABLED=true/false          - Enable/disable OCR caching
  EML_CACHE_DIR=path                    - Cache directory path
  EML_CACHE_EXPIRATION_DAYS=30          - Cache expiration in days
  EML_CACHE_MAX_SIZE_MB=1024            - Maximum cache size in MB
  EML_CACHE_CLEANUP_ON_STARTUP=true     - Cleanup expired entries on startup

Threading Configuration:
  EML_MAX_WORKER_THREADS=4              - Maximum worker threads
  EML_ENABLE_THREADING=true/false       - Enable/disable threading

OCR Configuration:
  EML_OCR_ENABLED=true/false            - Enable/disable OCR processing
  EML_OCR_TIMEOUT_SECONDS=300           - OCR timeout in seconds

Export Configuration:
  EML_EXCEL_OPTIMIZATION=true/false     - Enable Excel export optimization

GUI/Logging Preferences:
  EML_GUI_LOG_COLORIZE=true/false       - Colorize WARNING/ERROR in GUI log

Example:
  set EML_CACHE_ENABLED=true
  set EML_CACHE_EXPIRATION_DAYS=7
  set EML_MAX_WORKER_THREADS=2
"""


def print_env_help():
    """Print environment variables help"""
    print(ENV_VARS_HELP)
