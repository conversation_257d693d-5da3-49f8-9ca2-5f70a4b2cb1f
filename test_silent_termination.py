#!/usr/bin/env python3
"""
Test Script for Silent Termination Issues
Creates controlled test scenarios to reproduce silent program termination
"""

import os
import sys
import time
import shutil
import tempfile
import subprocess
from pathlib import Path
from datetime import datetime

class SilentTerminationTester:
    """Test various scenarios that might cause silent termination"""
    
    def __init__(self):
        self.test_dir = Path("test_silent_termination")
        self.results_dir = Path("test_results")
        self.email_processor_path = Path("email_processor.py")
        
    def setup_test_environment(self):
        """Set up test directories and files"""
        print("Setting up test environment...")
        
        # Create test directories
        self.test_dir.mkdir(exist_ok=True)
        self.results_dir.mkdir(exist_ok=True)
        
        # Clean up any previous test files
        for file in self.test_dir.glob("*.eml"):
            file.unlink()
    
    def create_test_email(self, filename, size_mb=1, with_attachments=False, with_large_images=False):
        """Create a test email file with specified characteristics"""
        email_content = f"""From: <EMAIL>
To: <EMAIL>
Subject: Test Email {filename}
Date: {datetime.now().strftime('%a, %d %b %Y %H:%M:%S %z')}
Content-Type: multipart/mixed; boundary="boundary123"

--boundary123
Content-Type: text/plain; charset=utf-8

This is a test email for silent termination testing.
"""
        
        # Add large text content to reach desired size
        if size_mb > 0:
            # Calculate approximate content size needed
            target_size = size_mb * 1024 * 1024
            current_size = len(email_content.encode('utf-8'))
            remaining_size = target_size - current_size
            
            if remaining_size > 0:
                # Add repeated content to reach target size
                filler_text = "This is filler text to increase email size. " * 100
                repetitions = remaining_size // len(filler_text.encode('utf-8'))
                email_content += filler_text * repetitions
        
        if with_attachments:
            # Add a large attachment
            email_content += """
--boundary123
Content-Type: application/octet-stream
Content-Disposition: attachment; filename="large_file.dat"
Content-Transfer-Encoding: base64

"""
            # Add base64 encoded data (approximately 1MB)
            import base64
            large_data = b"X" * (1024 * 1024)  # 1MB of data
            encoded_data = base64.b64encode(large_data).decode('ascii')
            email_content += encoded_data
        
        if with_large_images:
            # Add a large embedded image
            email_content += """
--boundary123
Content-Type: image/png
Content-Disposition: inline; filename="large_image.png"
Content-Transfer-Encoding: base64

"""
            # Create a large fake image (base64 encoded)
            import base64
            # Simulate a large PNG image
            fake_image_data = b"PNG_HEADER" + (b"PIXEL_DATA" * 100000)  # ~1MB fake image
            encoded_image = base64.b64encode(fake_image_data).decode('ascii')
            email_content += encoded_image
        
        email_content += "\n--boundary123--\n"
        
        # Write to file
        email_path = self.test_dir / filename
        with open(email_path, 'w', encoding='utf-8') as f:
            f.write(email_content)
        
        return email_path
    
    def run_test_scenario(self, scenario_name, email_files, timeout=300):
        """Run a test scenario with the email processor"""
        print(f"\n{'='*60}")
        print(f"Running test scenario: {scenario_name}")
        print(f"Email files: {len(email_files)}")
        print(f"Timeout: {timeout} seconds")
        print(f"{'='*60}")
        
        # Create output path
        output_path = self.results_dir / f"{scenario_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        # Prepare command
        cmd = [
            sys.executable, str(self.email_processor_path),
            "--batch", str(self.test_dir),
            "--output", str(output_path),
            "--no-gui"  # Assume there's a no-gui option
        ]
        
        print(f"Command: {' '.join(cmd)}")
        print(f"Working directory: {os.getcwd()}")
        
        # Start the process
        start_time = time.time()
        try:
            result = subprocess.run(
                cmd,
                timeout=timeout,
                capture_output=True,
                text=True,
                cwd=os.getcwd()
            )
            
            end_time = time.time()
            runtime = end_time - start_time
            
            print(f"\nProcess completed in {runtime:.2f} seconds")
            print(f"Return code: {result.returncode}")
            
            if result.stdout:
                print(f"STDOUT:\n{result.stdout}")
            
            if result.stderr:
                print(f"STDERR:\n{result.stderr}")
            
            # Check if output file was created
            if output_path.exists():
                file_size = output_path.stat().st_size
                print(f"Output file created: {output_path} ({file_size:,} bytes)")
            else:
                print("❌ Output file was NOT created")
            
            return {
                'scenario': scenario_name,
                'success': result.returncode == 0,
                'return_code': result.returncode,
                'runtime': runtime,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'output_created': output_path.exists(),
                'output_size': output_path.stat().st_size if output_path.exists() else 0
            }
            
        except subprocess.TimeoutExpired:
            print(f"❌ Process TIMED OUT after {timeout} seconds")
            return {
                'scenario': scenario_name,
                'success': False,
                'return_code': -1,
                'runtime': timeout,
                'stdout': '',
                'stderr': 'Process timed out',
                'output_created': False,
                'output_size': 0,
                'timeout': True
            }
        except Exception as e:
            print(f"❌ Process failed to start: {e}")
            return {
                'scenario': scenario_name,
                'success': False,
                'return_code': -2,
                'runtime': 0,
                'stdout': '',
                'stderr': str(e),
                'output_created': False,
                'output_size': 0,
                'error': str(e)
            }
    
    def test_memory_exhaustion(self):
        """Test scenario: Memory exhaustion with large emails"""
        print("Creating large email files for memory exhaustion test...")
        
        email_files = []
        # Create 10 emails, each ~50MB
        for i in range(10):
            filename = f"large_email_{i:02d}.eml"
            email_path = self.create_test_email(filename, size_mb=50, with_attachments=True)
            email_files.append(email_path)
        
        return self.run_test_scenario("memory_exhaustion", email_files, timeout=600)
    
    def test_many_small_files(self):
        """Test scenario: Many small files (threading stress)"""
        print("Creating many small email files for threading test...")
        
        email_files = []
        # Create 100 small emails
        for i in range(100):
            filename = f"small_email_{i:03d}.eml"
            email_path = self.create_test_email(filename, size_mb=0.1)
            email_files.append(email_path)
        
        return self.run_test_scenario("many_small_files", email_files, timeout=300)
    
    def test_ocr_intensive(self):
        """Test scenario: OCR-intensive processing"""
        print("Creating emails with large images for OCR test...")
        
        email_files = []
        # Create 20 emails with large embedded images
        for i in range(20):
            filename = f"ocr_email_{i:02d}.eml"
            email_path = self.create_test_email(filename, size_mb=5, with_large_images=True)
            email_files.append(email_path)
        
        return self.run_test_scenario("ocr_intensive", email_files, timeout=900)
    
    def test_mixed_workload(self):
        """Test scenario: Mixed workload (realistic scenario)"""
        print("Creating mixed workload for realistic test...")
        
        email_files = []
        # Mix of different email types
        for i in range(50):
            if i % 5 == 0:
                # Large email with attachments
                filename = f"mixed_large_{i:02d}.eml"
                email_path = self.create_test_email(filename, size_mb=10, with_attachments=True)
            elif i % 3 == 0:
                # Email with images
                filename = f"mixed_image_{i:02d}.eml"
                email_path = self.create_test_email(filename, size_mb=2, with_large_images=True)
            else:
                # Regular small email
                filename = f"mixed_regular_{i:02d}.eml"
                email_path = self.create_test_email(filename, size_mb=0.5)
            
            email_files.append(email_path)
        
        return self.run_test_scenario("mixed_workload", email_files, timeout=600)
    
    def run_all_tests(self):
        """Run all test scenarios"""
        print("Starting Silent Termination Test Suite")
        print(f"Test directory: {self.test_dir.absolute()}")
        print(f"Results directory: {self.results_dir.absolute()}")
        
        self.setup_test_environment()
        
        test_results = []
        
        # Run each test scenario
        scenarios = [
            ("Memory Exhaustion", self.test_memory_exhaustion),
            ("Many Small Files", self.test_many_small_files),
            ("OCR Intensive", self.test_ocr_intensive),
            ("Mixed Workload", self.test_mixed_workload)
        ]
        
        for scenario_name, test_func in scenarios:
            try:
                result = test_func()
                test_results.append(result)
                
                # Clean up test files between scenarios
                for file in self.test_dir.glob("*.eml"):
                    file.unlink()
                    
            except Exception as e:
                print(f"❌ Test scenario '{scenario_name}' failed: {e}")
                test_results.append({
                    'scenario': scenario_name,
                    'success': False,
                    'error': str(e)
                })
        
        # Print summary
        print(f"\n{'='*60}")
        print("TEST SUMMARY")
        print(f"{'='*60}")
        
        for result in test_results:
            status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
            scenario = result.get('scenario', 'Unknown')
            runtime = result.get('runtime', 0)
            
            print(f"{status} {scenario:<20} Runtime: {runtime:.2f}s")
            
            if not result.get('success', False):
                error = result.get('stderr', result.get('error', 'Unknown error'))
                print(f"     Error: {error}")
        
        return test_results

def main():
    """Main function"""
    tester = SilentTerminationTester()
    
    if not tester.email_processor_path.exists():
        print(f"❌ Email processor not found: {tester.email_processor_path}")
        print("Please run this script from the same directory as email_processor.py")
        return 1
    
    try:
        results = tester.run_all_tests()
        
        # Save results to JSON
        import json
        results_file = tester.results_dir / f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\nDetailed results saved to: {results_file}")
        
        # Return appropriate exit code
        failed_tests = [r for r in results if not r.get('success', False)]
        return len(failed_tests)
        
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
