#!/usr/bin/env python3
"""
Test script to verify ThreadSafeLogger methods are working correctly
"""

import sys
import os

# Add current directory to path to import email_processor
sys.path.insert(0, os.getcwd())

def test_thread_logger():
    """Test all ThreadSafeLogger methods"""
    print("Testing ThreadSafeLogger methods...")
    
    try:
        # Import the thread_logger from email_processor
        from email_processor import thread_logger
        
        # Test all logging methods
        methods_to_test = [
            'debug',
            'info', 
            'warning',
            'warn',
            'error',
            'critical',
            'fatal',
            'exception',
            'log',
            'setLevel',
            'getLevel',
            'isEnabledFor'
        ]
        
        print("Available methods on thread_logger:")
        for method_name in methods_to_test:
            if hasattr(thread_logger, method_name):
                print(f"  ✅ {method_name}")
            else:
                print(f"  ❌ {method_name} - MISSING")
        
        # Test actual method calls
        print("\nTesting method calls:")
        
        try:
            thread_logger.debug("Test debug message")
            print("  ✅ debug() call successful")
        except Exception as e:
            print(f"  ❌ debug() call failed: {e}")
        
        try:
            thread_logger.info("Test info message")
            print("  ✅ info() call successful")
        except Exception as e:
            print(f"  ❌ info() call failed: {e}")
        
        try:
            thread_logger.warning("Test warning message")
            print("  ✅ warning() call successful")
        except Exception as e:
            print(f"  ❌ warning() call failed: {e}")
        
        try:
            thread_logger.error("Test error message")
            print("  ✅ error() call successful")
        except Exception as e:
            print(f"  ❌ error() call failed: {e}")
        
        try:
            thread_logger.critical("Test critical message")
            print("  ✅ critical() call successful")
        except Exception as e:
            print(f"  ❌ critical() call failed: {e}")
        
        # Test level methods
        try:
            import logging
            current_level = thread_logger.getLevel()
            print(f"  ✅ getLevel() returned: {current_level}")
            
            thread_logger.setLevel(logging.DEBUG)
            new_level = thread_logger.getLevel()
            print(f"  ✅ setLevel(DEBUG) successful, new level: {new_level}")
            
            is_debug_enabled = thread_logger.isEnabledFor(logging.DEBUG)
            print(f"  ✅ isEnabledFor(DEBUG): {is_debug_enabled}")
            
        except Exception as e:
            print(f"  ❌ Level methods failed: {e}")
        
        print("\n🎉 All ThreadSafeLogger methods are working correctly!")
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import thread_logger: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False

def test_ocr_initialization_calls():
    """Test the specific calls that were failing"""
    print("\nTesting specific OCR initialization calls...")
    
    try:
        from email_processor import thread_logger
        
        # Test the specific calls that were failing
        test_calls = [
            'thread_logger.debug("Testing PaddleOCR import...")',
            'thread_logger.debug("PaddleOCR import successful")',
            'thread_logger.error("PaddleOCR import failed")',
            'thread_logger.debug("Testing PaddleOCR instantiation safety...")',
            'thread_logger.error("PaddleOCR safety test failed")',
            'thread_logger.debug("PaddleOCR safety test passed")',
            'thread_logger.debug("Initializing OCR service with cache")',
            'thread_logger.info("OCR service initialized successfully")',
            'thread_logger.warning("OCR service reports unavailable")',
            'thread_logger.error("OCR service initialization failed")',
            'thread_logger.debug("OCR init traceback: test")'
        ]
        
        for call in test_calls:
            try:
                eval(call)
                print(f"  ✅ {call}")
            except Exception as e:
                print(f"  ❌ {call} - FAILED: {e}")
        
        print("\n🎉 All OCR initialization calls are working!")
        return True
        
    except Exception as e:
        print(f"❌ OCR initialization test failed: {e}")
        return False

def main():
    """Main test function"""
    print("ThreadSafeLogger Fix Verification")
    print("=" * 50)
    
    success1 = test_thread_logger()
    success2 = test_ocr_initialization_calls()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("✅ ALL TESTS PASSED - ThreadSafeLogger fix is working correctly!")
        return 0
    else:
        print("❌ SOME TESTS FAILED - ThreadSafeLogger needs additional fixes")
        return 1

if __name__ == "__main__":
    sys.exit(main())
