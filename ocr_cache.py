"""
OCR Result Caching System for Email Batch Processor
Provides efficient caching of OCR results using SHA-256 file content hashing and SQLite storage
"""

import os
import sqlite3
import hashlib
import threading
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Any, Tuple
from dataclasses import dataclass
import json


@dataclass
class CacheStats:
    """Statistics for cache performance monitoring"""
    hits: int = 0
    misses: int = 0
    total_lookups: int = 0
    cache_size: int = 0
    
    @property
    def hit_ratio(self) -> float:
        """Calculate cache hit ratio as percentage"""
        if self.total_lookups == 0:
            return 0.0
        return (self.hits / self.total_lookups) * 100.0


class OcrCache:
    """
    Thread-safe OCR result caching system using SQLite database
    
    Features:
    - SHA-256 content-based cache keys
    - Configurable cache expiration
    - Thread-safe operations
    - Cache statistics and monitoring
    - Graceful error handling and recovery
    """
    
    def __init__(self, cache_dir: str = ".cache", cache_enabled: bool = True,
                 expiration_days: int = 30, max_cache_size_mb: int = 1024):
        """
        Initialize OCR cache system
        
        Args:
            cache_dir: Directory to store cache database
            cache_enabled: Whether caching is enabled
            expiration_days: Number of days before cache entries expire
            max_cache_size_mb: Maximum cache size in MB (for monitoring)
        """
        self.cache_enabled = cache_enabled
        self.expiration_days = expiration_days
        self.max_cache_size_mb = max_cache_size_mb
        
        # Setup cache directory and database path
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.db_path = self.cache_dir / "ocr_cache.db"
        
        # Thread safety
        self._lock = threading.RLock()
        self._connection_lock = threading.local()
        
        # Statistics
        self._stats = CacheStats()
        self._stats_lock = threading.Lock()
        
        # Logger
        self.logger = logging.getLogger(__name__)
        
        # Initialize database
        if self.cache_enabled:
            self._initialize_database()
    
    def _get_connection(self) -> sqlite3.Connection:
        """Get thread-local database connection"""
        if not hasattr(self._connection_lock, 'connection'):
            self._connection_lock.connection = sqlite3.connect(
                str(self.db_path),
                timeout=30.0,
                check_same_thread=False
            )
            # Enable WAL mode for better concurrent access
            self._connection_lock.connection.execute("PRAGMA journal_mode=WAL")
            self._connection_lock.connection.execute("PRAGMA synchronous=NORMAL")
            self._connection_lock.connection.execute("PRAGMA cache_size=10000")
        
        return self._connection_lock.connection
    
    def _initialize_database(self):
        """Initialize SQLite database with proper schema"""
        try:
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()
                
                # Create cache table with optimized schema
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS ocr_cache (
                        file_hash TEXT PRIMARY KEY,
                        ocr_text TEXT NOT NULL,
                        created_at TIMESTAMP NOT NULL,
                        file_size INTEGER NOT NULL,
                        original_filename TEXT,
                        access_count INTEGER DEFAULT 1,
                        last_accessed TIMESTAMP NOT NULL
                    )
                """)
                
                # Create indexes for performance
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_created_at 
                    ON ocr_cache(created_at)
                """)
                
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_last_accessed 
                    ON ocr_cache(last_accessed)
                """)
                
                conn.commit()
                self.logger.info(f"OCR cache database initialized at {self.db_path}")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize cache database: {e}")
            self.cache_enabled = False
    
    def _calculate_file_hash(self, file_data: bytes) -> str:
        """
        Calculate SHA-256 hash of file content
        
        Args:
            file_data: Raw file bytes
            
        Returns:
            SHA-256 hash as hexadecimal string
        """
        if not file_data:
            return hashlib.sha256(b"").hexdigest()
        
        return hashlib.sha256(file_data).hexdigest()
    
    def _update_stats(self, hit: bool):
        """Update cache statistics thread-safely"""
        with self._stats_lock:
            self._stats.total_lookups += 1
            if hit:
                self._stats.hits += 1
            else:
                self._stats.misses += 1
    
    def lookup(self, file_data: bytes, original_filename: str = "") -> Optional[str]:
        """
        Look up OCR result in cache
        
        Args:
            file_data: Raw file bytes to hash and lookup
            original_filename: Original filename for debugging
            
        Returns:
            Cached OCR text if found, None if not found or cache disabled
        """
        if not self.cache_enabled or not file_data:
            self._update_stats(False)
            return None
        
        try:
            file_hash = self._calculate_file_hash(file_data)
            
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()
                
                # Look up cache entry
                cursor.execute("""
                    SELECT ocr_text, created_at FROM ocr_cache 
                    WHERE file_hash = ?
                """, (file_hash,))
                
                result = cursor.fetchone()
                
                if result:
                    ocr_text, created_at_str = result
                    created_at = datetime.fromisoformat(created_at_str)
                    
                    # Check if entry has expired
                    expiration_threshold = timedelta(days=self.expiration_days) if self.expiration_days > 0 else timedelta(seconds=1)
                    if datetime.now() - created_at > expiration_threshold:
                        self.logger.debug(f"Cache entry expired for hash {file_hash[:8]}...")
                        # Remove expired entry
                        cursor.execute("DELETE FROM ocr_cache WHERE file_hash = ?", (file_hash,))
                        conn.commit()
                        self._update_stats(False)
                        return None
                    
                    # Update access statistics
                    cursor.execute("""
                        UPDATE ocr_cache 
                        SET access_count = access_count + 1, last_accessed = ?
                        WHERE file_hash = ?
                    """, (datetime.now().isoformat(), file_hash))
                    conn.commit()
                    
                    self.logger.debug(f"Cache hit for {original_filename} (hash: {file_hash[:8]}...)")
                    self._update_stats(True)
                    return ocr_text
                else:
                    self.logger.debug(f"Cache miss for {original_filename} (hash: {file_hash[:8]}...)")
                    self._update_stats(False)
                    return None
                    
        except Exception as e:
            self.logger.error(f"Cache lookup failed: {e}")
            self._update_stats(False)
            return None
    
    def store(self, file_data: bytes, ocr_text: str, original_filename: str = "") -> bool:
        """
        Store OCR result in cache
        
        Args:
            file_data: Raw file bytes to hash
            ocr_text: OCR result text to cache
            original_filename: Original filename for debugging
            
        Returns:
            True if stored successfully, False otherwise
        """
        if not self.cache_enabled or not file_data:
            return False
        
        try:
            file_hash = self._calculate_file_hash(file_data)
            file_size = len(file_data)
            now = datetime.now().isoformat()
            
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()
                
                # Insert or replace cache entry
                cursor.execute("""
                    INSERT OR REPLACE INTO ocr_cache 
                    (file_hash, ocr_text, created_at, file_size, original_filename, 
                     access_count, last_accessed)
                    VALUES (?, ?, ?, ?, ?, 1, ?)
                """, (file_hash, ocr_text, now, file_size, original_filename, now))
                
                conn.commit()
                
                self.logger.debug(f"Cached OCR result for {original_filename} (hash: {file_hash[:8]}...)")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to store cache entry: {e}")
            return False
    
    def get_stats(self) -> CacheStats:
        """Get current cache statistics"""
        with self._stats_lock:
            # Update cache size from database
            try:
                if self.cache_enabled:
                    with self._lock:
                        conn = self._get_connection()
                        cursor = conn.cursor()
                        cursor.execute("SELECT COUNT(*) FROM ocr_cache")
                        self._stats.cache_size = cursor.fetchone()[0]
            except Exception as e:
                self.logger.error(f"Failed to get cache size: {e}")
            
            return CacheStats(
                hits=self._stats.hits,
                misses=self._stats.misses,
                total_lookups=self._stats.total_lookups,
                cache_size=self._stats.cache_size
            )
    
    def clear_cache(self) -> bool:
        """
        Clear all cache entries
        
        Returns:
            True if cleared successfully, False otherwise
        """
        if not self.cache_enabled:
            return False
        
        try:
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()
                cursor.execute("DELETE FROM ocr_cache")
                conn.commit()
                
                # Reset statistics
                with self._stats_lock:
                    self._stats.cache_size = 0
                
                self.logger.info("Cache cleared successfully")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to clear cache: {e}")
            return False
    
    def cleanup_expired(self) -> int:
        """
        Remove expired cache entries
        
        Returns:
            Number of entries removed
        """
        if not self.cache_enabled:
            return 0
        
        try:
            expiration_threshold = timedelta(days=self.expiration_days) if self.expiration_days > 0 else timedelta(seconds=1)
            cutoff_date = (datetime.now() - expiration_threshold).isoformat()
            
            with self._lock:
                conn = self._get_connection()
                cursor = conn.cursor()
                
                # Count entries to be removed
                cursor.execute("SELECT COUNT(*) FROM ocr_cache WHERE created_at < ?", (cutoff_date,))
                count = cursor.fetchone()[0]
                
                # Remove expired entries
                cursor.execute("DELETE FROM ocr_cache WHERE created_at < ?", (cutoff_date,))
                conn.commit()
                
                self.logger.info(f"Cleaned up {count} expired cache entries")
                return count
                
        except Exception as e:
            self.logger.error(f"Failed to cleanup expired entries: {e}")
            return 0
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get comprehensive cache information"""
        stats = self.get_stats()
        
        info = {
            'enabled': self.cache_enabled,
            'database_path': str(self.db_path),
            'expiration_days': self.expiration_days,
            'max_size_mb': self.max_cache_size_mb,
            'statistics': {
                'total_entries': stats.cache_size,
                'total_lookups': stats.total_lookups,
                'cache_hits': stats.hits,
                'cache_misses': stats.misses,
                'hit_ratio_percent': round(stats.hit_ratio, 2)
            }
        }
        
        # Add database file size if available
        try:
            if self.db_path.exists():
                db_size_mb = self.db_path.stat().st_size / (1024 * 1024)
                info['database_size_mb'] = round(db_size_mb, 2)
        except Exception:
            pass
        
        return info
    
    def close(self):
        """Close database connections and cleanup resources"""
        try:
            if hasattr(self._connection_lock, 'connection'):
                self._connection_lock.connection.close()
                delattr(self._connection_lock, 'connection')
        except Exception as e:
            self.logger.error(f"Error closing cache connection: {e}")


# Global cache instance (will be initialized by configuration)
_global_cache: Optional[OcrCache] = None


def get_cache() -> Optional[OcrCache]:
    """Get the global cache instance"""
    return _global_cache


def initialize_cache(cache_dir: str = ".cache", cache_enabled: bool = True,
                    expiration_days: int = 30, max_cache_size_mb: int = 1024) -> OcrCache:
    """
    Initialize the global cache instance
    
    Args:
        cache_dir: Directory to store cache database
        cache_enabled: Whether caching is enabled
        expiration_days: Number of days before cache entries expire
        max_cache_size_mb: Maximum cache size in MB
        
    Returns:
        Initialized cache instance
    """
    global _global_cache
    _global_cache = OcrCache(
        cache_dir=cache_dir,
        cache_enabled=cache_enabled,
        expiration_days=expiration_days,
        max_cache_size_mb=max_cache_size_mb
    )
    return _global_cache


def shutdown_cache():
    """Shutdown the global cache instance"""
    global _global_cache
    if _global_cache:
        _global_cache.close()
        _global_cache = None
