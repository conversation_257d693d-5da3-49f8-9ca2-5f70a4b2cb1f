#!/usr/bin/env python3
"""
OCR Cache Management Utility
Provides command-line tools for managing the OCR cache system
"""

import sys
import argparse
from pathlib import Path
from typing import Optional

# Import cache and configuration systems
try:
    from ocr_cache import initialize_cache, get_cache, shutdown_cache
    from config import get_config, get_cache_config, print_config, print_env_help
    CACHE_AVAILABLE = True
except ImportError as e:
    print(f"❌ Error: Required modules not available: {e}")
    CACHE_AVAILABLE = False
    sys.exit(1)


def format_size(size_bytes: int) -> str:
    """Format size in bytes to human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"


def print_cache_stats():
    """Print detailed cache statistics"""
    cache = get_cache()
    if not cache:
        print("❌ Cache not initialized")
        return
    
    stats = cache.get_stats()
    info = cache.get_cache_info()
    
    print("\n📊 OCR Cache Statistics:")
    print("=" * 50)
    print(f"Status: {'✅ Enabled' if info['enabled'] else '❌ Disabled'}")
    
    if info['enabled']:
        print(f"Database: {info['database_path']}")
        if 'database_size_mb' in info:
            print(f"Database Size: {info['database_size_mb']} MB")
        
        print(f"Cache Entries: {info['statistics']['total_entries']:,}")
        print(f"Total Lookups: {info['statistics']['total_lookups']:,}")
        print(f"Cache Hits: {info['statistics']['cache_hits']:,}")
        print(f"Cache Misses: {info['statistics']['cache_misses']:,}")
        print(f"Hit Ratio: {info['statistics']['hit_ratio_percent']:.1f}%")
        print(f"Expiration: {info['expiration_days']} days")
        print(f"Max Size Limit: {info['max_size_mb']} MB")
    
    print("=" * 50)


def clear_cache():
    """Clear all cache entries"""
    cache = get_cache()
    if not cache:
        print("❌ Cache not initialized")
        return False
    
    print("🗑️ Clearing OCR cache...")
    success = cache.clear_cache()
    
    if success:
        print("✅ Cache cleared successfully")
    else:
        print("❌ Failed to clear cache")
    
    return success


def cleanup_expired():
    """Clean up expired cache entries"""
    cache = get_cache()
    if not cache:
        print("❌ Cache not initialized")
        return 0
    
    print("🧹 Cleaning up expired cache entries...")
    removed_count = cache.cleanup_expired()
    
    if removed_count > 0:
        print(f"✅ Removed {removed_count} expired entries")
    else:
        print("ℹ️ No expired entries found")
    
    return removed_count


def test_cache():
    """Test cache functionality with sample data"""
    cache = get_cache()
    if not cache:
        print("❌ Cache not initialized")
        return False
    
    print("🧪 Testing cache functionality...")
    
    # Test data
    test_data = b"This is test image data for cache testing"
    test_result = "This is test OCR result"
    
    # Test storage
    print("  Testing cache storage...")
    store_success = cache.store(test_data, test_result, "test_file.jpg")
    if not store_success:
        print("❌ Cache storage test failed")
        return False
    print("  ✅ Storage test passed")
    
    # Test lookup
    print("  Testing cache lookup...")
    cached_result = cache.lookup(test_data, "test_file.jpg")
    if cached_result != test_result:
        print(f"❌ Cache lookup test failed: expected '{test_result}', got '{cached_result}'")
        return False
    print("  ✅ Lookup test passed")
    
    # Clean up test data
    print("  Cleaning up test data...")
    cache.clear_cache()
    
    print("✅ All cache tests passed")
    return True


def initialize_cache_system():
    """Initialize the cache system with current configuration"""
    try:
        cache_config = get_cache_config()
        cache = initialize_cache(
            cache_dir=cache_config.cache_dir,
            cache_enabled=cache_config.enabled,
            expiration_days=cache_config.expiration_days,
            max_cache_size_mb=cache_config.max_cache_size_mb
        )
        
        if cache_config.cleanup_on_startup:
            print("🧹 Performing startup cleanup...")
            removed = cache.cleanup_expired()
            if removed > 0:
                print(f"✅ Removed {removed} expired entries during startup")
        
        print("✅ Cache system initialized successfully")
        return cache
    except Exception as e:
        print(f"❌ Failed to initialize cache system: {e}")
        return None


def main():
    """Main command-line interface"""
    parser = argparse.ArgumentParser(
        description="OCR Cache Management Utility",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python cache_manager.py stats          # Show cache statistics
  python cache_manager.py clear          # Clear all cache entries
  python cache_manager.py cleanup        # Remove expired entries
  python cache_manager.py test           # Test cache functionality
  python cache_manager.py config         # Show current configuration
  python cache_manager.py env-help       # Show environment variables help
        """
    )
    
    parser.add_argument(
        'command',
        choices=['stats', 'clear', 'cleanup', 'test', 'config', 'env-help'],
        help='Command to execute'
    )
    
    parser.add_argument(
        '--cache-dir',
        default=None,
        help='Override cache directory'
    )
    
    parser.add_argument(
        '--no-cleanup',
        action='store_true',
        help='Skip automatic cleanup on startup'
    )
    
    if len(sys.argv) == 1:
        parser.print_help()
        return
    
    args = parser.parse_args()
    
    # Handle config and env-help commands that don't need cache initialization
    if args.command == 'config':
        print_config()
        return
    
    if args.command == 'env-help':
        print_env_help()
        return
    
    # Initialize cache system
    cache = initialize_cache_system()
    if not cache and args.command != 'config':
        print("❌ Cannot proceed without cache system")
        sys.exit(1)
    
    try:
        # Execute commands
        if args.command == 'stats':
            print_cache_stats()
        
        elif args.command == 'clear':
            clear_cache()
        
        elif args.command == 'cleanup':
            cleanup_expired()
        
        elif args.command == 'test':
            success = test_cache()
            if not success:
                sys.exit(1)
        
    except KeyboardInterrupt:
        print("\n⚠️ Operation cancelled by user")
    except Exception as e:
        print(f"❌ Error executing command: {e}")
        sys.exit(1)
    finally:
        # Cleanup
        shutdown_cache()


if __name__ == '__main__':
    main()
